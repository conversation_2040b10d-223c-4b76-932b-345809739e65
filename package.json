{"name": "next15-tailwind", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "start:static": "npx serve@latest out", "lint": "next lint && tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "lint:fix": "next lint --fix && tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "format": "pretty-quick && tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "format:staged": "pretty-quick --staged && tsc --noEmit --skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@hookstate/core": "^4.0.2", "@hookstate/devtools": "^4.0.3", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5", "@tanstack/react-query-devtools": "^5", "axios": "^1", "class-variance-authority": "^0.7.1", "clsx": "^2", "cmdk": "^1", "connectkit": "^1.9.1", "date-fns": "^4", "embla-carousel-react": "^8", "input-otp": "^1.4.2", "lucide-react": "^0.535.0", "next": "15.4.5", "next-themes": "^0.4.6", "react": "^19", "react-day-picker": "^9", "react-dom": "^19", "react-hook-form": "^7", "react-icons": "^5.5.0", "react-resizable-panels": "^3", "react-unity-webgl": "^10.1.5", "recharts": "^2", "sonner": "^2", "tailwind-merge": "^3", "vaul": "^1", "viem": "2.x", "wagmi": "^2.16.1", "zod": "^3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@ianvs/prettier-plugin-sort-imports": "^4", "@tailwindcss/postcss": "^4", "@tanstack/eslint-plugin-query": "^5", "@types/lodash.merge": "^4.6.9", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "eslint-config-prettier": "^10", "eslint-plugin-prettier": "^5", "eslint-plugin-unused-imports": "^4", "husky": "^9", "prettier": "^3", "prettier-plugin-tailwindcss": "^0.6", "pretty-quick": "^4", "tailwindcss": "^4", "tw-animate-css": "^1", "typescript": "^5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}