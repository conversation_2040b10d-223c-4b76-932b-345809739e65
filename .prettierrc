{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": true, "endOfLine": "auto", "plugins": ["@ianvs/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "tailwindFunctions": ["clsx"], "tailwindPreserveDuplicates": true, "tailwindPreserveWhitespace": true, "importOrder": ["node", "", "<THIRD_PARTY_MODULES>", "", "^@/(.*)$", "", "^[./]", "", "^\\./(.\\.(css|scss|sass|less))$"], "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "importOrderTypeScriptVersion": "5.0.0", "importOrderCaseSensitive": false}