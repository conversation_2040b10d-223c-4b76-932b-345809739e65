'use client';

import { ConnectKitButton } from 'connectkit';
import { useEffect, useRef } from 'react';
import { Unity, useUnityContext } from 'react-unity-webgl';
import { useAccount } from 'wagmi';

declare global {
	interface Window {
		onUnityConnectWalletRequest?: () => void;
	}
}

export default function UnityWrapper() {
	const { address, isConnected } = useAccount();
	const connectButtonRef = useRef<HTMLDivElement>(null);

	const { unityProvider, sendMessage } = useUnityContext({
		loaderUrl: '/unity/build/Build.loader.js',
		dataUrl: '/unity/build/Build.data',
		frameworkUrl: '/unity/build/Build.framework.js',
		codeUrl: '/unity/build/Build.wasm',
	});

	useEffect(() => {
		window.onUnityConnectWalletRequest = async () => {
			try {
				// If already connected, send the current address to Unity
				if (isConnected && address) {
					sendMessage('WalletBridgeManager', 'OnWalletConnected', address);
					return;
				}

				// Trigger the ConnectButton modal by clicking it
				if (connectButtonRef.current) {
					const button = connectButtonRef.current.querySelector('button');
					if (button) {
						button.click();
					}
				}
			} catch (error) {
				if (error instanceof Error) {
					alert(`Wallet connection failed: ${error.message}`);
				} else {
					alert('Wallet connection failed: An unknown error occurred.');
				}
			}
		};
	}, [sendMessage, isConnected, address]);

	// Send address to Unity when connection status changes
	useEffect(() => {
		if (isConnected && address) {
			sendMessage('WalletBridgeManager', 'OnWalletConnected', address);
		}
	}, [isConnected, address, sendMessage]);

	return (
		<div className="font-inter flex min-h-screen items-center  justify-center">
			<Unity
				unityProvider={unityProvider}
				style={{ width: 960, height: 600 }}
			/>

			{/* Hidden ConnectButton that can be triggered programmatically */}
			<div
				ref={connectButtonRef}
				style={{
					position: 'absolute',
					left: '-9999px',
					opacity: 0,
					pointerEvents: 'none',
				}}
			>
				<ConnectKitButton />
			</div>
		</div>
	);
}
