'use client';

import { useState } from 'react';

// Hook
function useLocalStorage<T>(key: string, initialValue: T) {
	// State to store our value
	const [storedValue, setStoredValue] = useState<T>(() => {
		try {
			// Get from local storage by key
			if (typeof window !== 'undefined') {
				const item = window.localStorage.getItem(key);
				return item ? JSON.parse(item) : initialValue;
			}

			// Parse stored json or if none return initialValue
			return initialValue;
		} catch (error) {
			console.error(error);
			return initialValue;
		}
	});

	// Return a wrapped version of useState's setter function
	const setValue = (value: T | ((val: T) => T)) => {
		try {
			// Allow value to be a function so we have same API as useState
			const valueToStore =
				value instanceof Function ? value(storedValue) : value;
			// Save state
			setStoredValue(valueToStore);
			// Save to local storage
			if (typeof window !== 'undefined') {
				window.localStorage.setItem(key, JSON.stringify(valueToStore));
			}
		} catch (error) {
			console.error(error);
		}
	};

	return [storedValue, setValue] as const;
}

export default useLocalStorage;
